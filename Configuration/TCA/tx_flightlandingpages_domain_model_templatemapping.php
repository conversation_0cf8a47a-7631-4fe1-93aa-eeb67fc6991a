<?php
return [
    'ctrl' => [
        'title' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tx_flightlandingpages_domain_model_templatemapping',
        'label' => 'origin_type',
        'label_alt' => 'destination_type,template_page_uid',
        'label_alt_force' => true,
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',
        'delete' => 'deleted',
        'enablecolumns' => [
            'disabled' => 'hidden',
        ],
        'searchFields' => 'origin_type,destination_type',
        'iconfile' => 'EXT:flight_landing_pages/Resources/Public/Icons/tx_flightlandingpages_domain_model_templatemapping.svg'
    ],
    'types' => [
        '1' => [
            'showitem' => '
                --palette--;;type_combination_palette,
                template_page_uid,
                --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:access,
                hidden
            '
        ]
    ],
    'palettes' => [
        'type_combination_palette' => [
            'label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tx_flightlandingpages_domain_model_templatemapping.type_combination',
            'showitem' => 'origin_type, destination_type'
        ]
    ],
    'columns' => [
        'hidden' => [
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.visible',
            'config' => [
                'type' => 'check',
                'renderType' => 'checkboxToggle',
                'items' => [
                    [
                        'label' => '',
                        'value' => '',
                        'invertStateDisplay' => true
                    ]
                ],
            ],
        ],
        'landing_page_uid' => [
            'exclude' => true,
            'label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tx_flightlandingpages_domain_model_templatemapping.landing_page_uid',
            'config' => [
                'type' => 'group',
                'allowed' => 'pages',
                'size' => 1,
                'maxitems' => 1,
                'minitems' => 1,
                'default' => 0,
                'suggestOptions' => [
                    'default' => [
                        'additionalSearchFields' => 'nav_title, alias, url',
                        'addWhere' => 'AND pages.doktype = 201'
                    ]
                ]
            ]
        ],
        'origin_type' => [
            'exclude' => true,
            'label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tx_flightlandingpages_domain_model_templatemapping.origin_type',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'items' => [
                    ['label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tx_flightlandingpages_domain_model_flightroute.origin_type.airport', 'value' => 'airport'],
                    ['label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tx_flightlandingpages_domain_model_flightroute.origin_type.city', 'value' => 'city'],
                    ['label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tx_flightlandingpages_domain_model_flightroute.origin_type.country', 'value' => 'country'],
                ],
                'default' => 'airport',
                'required' => true,
            ],
        ],
        'destination_type' => [
            'exclude' => true,
            'label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tx_flightlandingpages_domain_model_templatemapping.destination_type',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'items' => [
                    ['label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tx_flightlandingpages_domain_model_flightroute.destination_type.airport', 'value' => 'airport'],
                    ['label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tx_flightlandingpages_domain_model_flightroute.destination_type.city', 'value' => 'city'],
                    ['label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tx_flightlandingpages_domain_model_flightroute.destination_type.country', 'value' => 'country'],
                ],
                'default' => 'airport',
                'required' => true,
            ],
        ],
        'template_page_uid' => [
            'exclude' => true,
            'label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tx_flightlandingpages_domain_model_templatemapping.template_page_uid',
            'config' => [
                'type' => 'group',
                'allowed' => 'pages',
                'size' => 1,
                'maxitems' => 1,
                'minitems' => 1,
                'default' => 0,
                'suggestOptions' => [
                    'default' => [
                        'additionalSearchFields' => 'nav_title, alias, url',
                        'addWhere' => 'AND pages.doktype = 200'
                    ]
                ]
            ]
        ],
    ],
];
