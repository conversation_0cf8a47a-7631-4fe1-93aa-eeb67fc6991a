<?php

/**
 * Simple test script to verify the Flight Routes XML Sitemap Data Provider
 * 
 * This script tests the sitemap provider functionality without requiring
 * a fully configured TYPO3 frontend.
 */

// Mock the required TYPO3 environment for testing
if (!defined('TYPO3')) {
    define('TYPO3', true);
}

// Include the autoloader
require_once '/var/www/html/vendor/autoload.php';

use Bgs\FlightLandingPages\XmlSitemap\FlightRoutesXmlSitemapDataProvider;
use TYPO3\CMS\Core\Http\ServerRequest;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Site\SiteInterface;

echo "Testing Flight Routes XML Sitemap Data Provider\n";
echo "===============================================\n\n";

try {
    // Create a mock request with a mock site
    $mockSite = new class implements SiteInterface {
        public function getIdentifier(): string { return 'test-site'; }
        public function getBase(): \Psr\Http\Message\UriInterface { 
            return new \TYPO3\CMS\Core\Http\Uri('https://example.com/'); 
        }
        public function getRootPageId(): int { return 1; }
        public function getLanguages(): array { return []; }
        public function getDefaultLanguage(): \TYPO3\CMS\Core\Site\Entity\SiteLanguage { 
            throw new \Exception('Not implemented'); 
        }
        public function getAvailableLanguages(\TYPO3\CMS\Core\Authentication\BackendUserAuthentication $user = null, bool $includeAllLanguagesFlag = false, int $pageId = null): array { return []; }
        public function getLanguageById(int $languageId): \TYPO3\CMS\Core\Site\Entity\SiteLanguage { 
            throw new \Exception('Not implemented'); 
        }
        public function getConfiguration(): array { return []; }
        public function getAttribute(string $name) { return null; }
        public function getRouter(\TYPO3\CMS\Core\Context\Context $context = null): \TYPO3\CMS\Core\Routing\RouterInterface { 
            throw new \Exception('Not implemented'); 
        }
        public function getErrorHandlers(): array { return []; }
        public function getErrorHandler(int $statusCode): \TYPO3\CMS\Core\Error\PageErrorHandler\PageErrorHandlerInterface { 
            throw new \Exception('Not implemented'); 
        }
        public function getSets(): \TYPO3\CMS\Core\Site\Set\SetRegistry { 
            throw new \Exception('Not implemented'); 
        }
    };

    $request = new ServerRequest('GET', 'https://example.com/?type=**********');
    $request = $request->withAttribute('site', $mockSite);

    // Create the sitemap provider
    $provider = new FlightRoutesXmlSitemapDataProvider($request, 'flightRoutes', []);

    echo "✓ Sitemap provider created successfully\n";

    // Test getLastModified
    $lastModified = $provider->getLastModified();
    echo "✓ getLastModified() returned: " . ($lastModified > 0 ? date('Y-m-d H:i:s', $lastModified) : '0') . "\n";

    // Test getItems
    $items = $provider->getItems();
    echo "✓ getItems() returned " . count($items) . " items\n";

    if (count($items) > 0) {
        echo "\nSample sitemap items:\n";
        foreach (array_slice($items, 0, 3) as $i => $item) {
            echo "  Item " . ($i + 1) . ":\n";
            echo "    URL: " . ($item['loc'] ?? 'N/A') . "\n";
            echo "    Last Modified: " . (isset($item['lastMod']) ? date('Y-m-d H:i:s', $item['lastMod']) : 'N/A') . "\n";
            echo "    Change Frequency: " . ($item['changefreq'] ?? 'N/A') . "\n";
            echo "    Priority: " . ($item['priority'] ?? 'N/A') . "\n";
            echo "\n";
        }
    } else {
        echo "\nNo sitemap items found. This could be because:\n";
        echo "- No active flight routes exist\n";
        echo "- Landing pages don't have sitemap enabled\n";
        echo "- Site configuration issues\n";
    }

    echo "\n✓ Test completed successfully!\n";

} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nTest Summary:\n";
echo "- The sitemap provider class loads correctly\n";
echo "- Basic functionality works\n";
echo "- Integration with TYPO3's sitemap system should work\n";
echo "\nTo use in production:\n";
echo "1. Ensure landing pages have 'Enable in Sitemap' checked\n";
echo "2. Configure template page SEO settings\n";
echo "3. Access sitemap at: /?type=**********\n";
