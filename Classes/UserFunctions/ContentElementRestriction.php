<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\UserFunctions;

use TYPO3\CMS\Backend\Utility\BackendUtility;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Content Element Restriction for Flight Landing Pages
 *
 * Restricts certain content elements to specific page types
 */
class ContentElementRestriction
{
    /**
     * Restrict content elements based on page context
     *
     * This function can be called by TCA itemsProcFunc to filter available
     * content element types based on the current page type.
     *
     * Note: Currently not used to avoid conflicts with other extensions.
     * Content element restriction is handled through backend preview warnings.
     *
     * @param array $parameters TCA parameters
     */
    public function restrictContentElements(array &$parameters): void
    {
        // Get current page UID from various possible sources
        $pageUid = $this->getCurrentPageUid($parameters);

        if ($pageUid <= 0) {
            // If we can't determine the page, allow all content elements
            return;
        }

        // Get page record to check doktype
        $page = BackendUtility::getRecord('pages', $pageUid, 'doktype');

        if (!$page || (int)$page['doktype'] !== 201) {
            // Not a Landing Page (doktype 201), remove the restricted content element
            $this->removeRestrictedContentElement($parameters);
        }

        // If it is a Landing Page (doktype 201), keep all items as they are
    }

    /**
     * Legacy method for backward compatibility
     *
     * @param array $parameters TCA parameters
     */
    public function restrictToLandingPages(array &$parameters): void
    {
        $this->restrictContentElements($parameters);
    }

    /**
     * Get current page UID from various sources
     *
     * @param array $parameters TCA parameters
     * @return int Page UID
     */
    protected function getCurrentPageUid(array $parameters): int
    {
        try {
            // 1. From the record being edited (for existing records)
            if (!empty($parameters['row']['pid'])) {
                return (int)$parameters['row']['pid'];
            }

            // 2. From GET/POST parameters (for new records)
            $pageUid = (int)($_GET['id'] ?? $_POST['id'] ?? 0);
            if ($pageUid > 0) {
                return $pageUid;
            }

            // 3. From edit parameters (when editing existing records)
            if (!empty($_GET['edit']['tt_content'])) {
                $editParams = $_GET['edit']['tt_content'];
                if (is_array($editParams)) {
                    $contentUid = (int)array_key_first($editParams);
                    if ($contentUid > 0) {
                        $contentRecord = BackendUtility::getRecord('tt_content', $contentUid, 'pid');
                        if ($contentRecord) {
                            return (int)$contentRecord['pid'];
                        }
                    }
                }
            }

            // 4. From returnUrl parameter (fallback)
            if (!empty($_GET['returnUrl'])) {
                $returnUrl = $_GET['returnUrl'];
                if (preg_match('/[?&]id=(\d+)/', $returnUrl, $matches)) {
                    return (int)$matches[1];
                }
            }

        } catch (\Exception $e) {
            // Silently handle any errors and return 0
        }

        return 0;
    }

    /**
     * Remove the restricted content element from available options
     *
     * @param array $parameters TCA parameters
     */
    protected function removeRestrictedContentElement(array &$parameters): void
    {
        $restrictedCType = 'flightlandingpages_destinationpairsmenu';

        if (!isset($parameters['items']) || !is_array($parameters['items'])) {
            return;
        }

        // Filter out the restricted content element
        $parameters['items'] = array_filter(
            $parameters['items'],
            function ($item) use ($restrictedCType) {
                // Handle both old array format [label, value, icon, group]
                // and new associative format ['label' => ..., 'value' => ...]
                if (is_array($item)) {
                    if (isset($item['value'])) {
                        return $item['value'] !== $restrictedCType;
                    } elseif (isset($item[1])) {
                        return $item[1] !== $restrictedCType;
                    }
                }
                return true;
            }
        );
    }
}
