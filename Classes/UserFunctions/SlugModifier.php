<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\UserFunctions;

use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Slug modifier for flight route slugs
 *
 * Includes parent page path to generated slugs
 * to match the expected format like "/flights/ber-sof"
 */
class SlugModifier
{
    /**
     * Add parent page path to the generated slug
     *
     * @param array $parameters Parameters containing slug and other data
     * @return string Modified slug with parent page path
     */
    public function addParentPagePath(array $parameters): string
    {
        $slug = $parameters['slug'] ?? '';
        $record = $parameters['record'] ?? [];
        $pid = (int)($parameters['pid'] ?? $record['pid'] ?? 0);

        // Generate type-aware slug if we have the necessary data
        if (!empty($record) && isset($record['origin_type']) && isset($record['destination_type'])) {
            $slug = $this->generateTypeAwareSlug($record);
        }

        // If slug is empty, return empty
        if (empty($slug)) {
            return '';
        }

        // Get parent page path
        $parentPagePath = $this->getParentPagePath($pid);

        // Combine parent page path with slug
        if (!empty($parentPagePath)) {
            // Remove leading slash from parent path and ensure proper formatting
            $parentPagePath = ltrim($parentPagePath, '/');
            $slug = $parentPagePath . '/' . $slug;
        }

        return $slug;
    }

    /**
     * Get the full path of the parent page including all parent pages
     *
     * @param int $pid Page ID
     * @return string Parent page path
     */
    protected function getParentPagePath(int $pid): string
    {
        if ($pid <= 0) {
            return '';
        }

        try {
            $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
                ->getQueryBuilderForTable('pages');

            $page = $queryBuilder
                ->select('uid', 'pid', 'slug', 'title')
                ->from('pages')
                ->where(
                    $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($pid, \PDO::PARAM_INT)),
                    $queryBuilder->expr()->eq('deleted', 0),
                    $queryBuilder->expr()->eq('hidden', 0)
                )
                ->executeQuery()
                ->fetchAssociative();

            if (!$page) {
                return '';
            }

            // Use the page's slug if available
            if (!empty($page['slug'])) {
                return $page['slug'];
            }

            // Fallback: build path from title if no slug
            return '/' . $this->sanitizeSlug($page['title']);

        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Generate type-aware slug based on origin and destination types
     *
     * @param array $record Flight route record data
     * @return string Generated slug
     */
    protected function generateTypeAwareSlug(array $record): string
    {
        $originPart = $this->generateSlugPart(
            $record['origin_name'] ?? '',
            $record['origin_code'] ?? '',
            $record['origin_type'] ?? 'airport'
        );

        $destinationPart = $this->generateSlugPart(
            $record['destination_name'] ?? '',
            $record['destination_code'] ?? '',
            $record['destination_type'] ?? 'airport'
        );

        if (empty($originPart) || empty($destinationPart)) {
            return '';
        }

        return $originPart . '-' . $destinationPart;
    }

    /**
     * Generate slug part based on type
     *
     * @param string $name Location name
     * @param string $code Location code
     * @param string $type Location type (airport, city, country)
     * @return string Slug part
     */
    protected function generateSlugPart(string $name, string $code, string $type): string
    {
        if ($type === 'airport') {
            return strtolower($code);
        }

        // For city and country, use sanitized name
        return $this->sanitizeNameForSlug($name);
    }

    /**
     * Sanitize name for slug (city/country names)
     *
     * @param string $name Location name
     * @return string Sanitized slug part
     */
    protected function sanitizeNameForSlug(string $name): string
    {
        if (empty($name)) {
            return '';
        }

        // Convert to ASCII (remove accents)
        $name = iconv('UTF-8', 'ASCII//TRANSLIT', $name);

        // Convert to lowercase
        $name = strtolower(trim($name));

        // Replace spaces with underscores
        $name = str_replace(' ', '_', $name);

        // Remove special characters except underscores and hyphens
        $name = preg_replace('/[^a-z0-9_\-]/', '', $name);

        // Handle multiple consecutive separators
        $name = preg_replace('/[_\-]+/', '_', $name);

        return trim($name, '_-');
    }

    /**
     * Simple slug sanitization for fallback cases
     *
     * @param string $value
     * @return string
     */
    protected function sanitizeSlug(string $value): string
    {
        // Basic sanitization - convert to lowercase, replace spaces and special chars with dashes
        $value = strtolower(trim($value));
        $value = preg_replace('/[^a-z0-9\-]/', '-', $value);
        $value = preg_replace('/-+/', '-', $value);
        return trim($value, '-');
    }
}
