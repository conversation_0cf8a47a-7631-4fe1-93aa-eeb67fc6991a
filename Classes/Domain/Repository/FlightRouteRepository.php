<?php
namespace Bgs\FlightLandingPages\Domain\Repository;

use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Site\SiteFinder;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Persistence\Repository;
use Bgs\FlightLandingPages\Domain\Model\FlightRoute;

class FlightRouteRepository extends Repository
{
    /**
     * Initialize the repository with proper storage page handling
     */
    public function initializeObject(): void
    {
        $querySettings = $this->createQuery()->getQuerySettings();

        // If no storage page is configured (storagePid = 0),
        // set it to the current page as fallback
        $storagePids = $querySettings->getStoragePageIds();
        if (empty($storagePids) || in_array(0, $storagePids, true)) {
            $currentPageId = (int)($GLOBALS['TSFE']->id ?? 0);
            if ($currentPageId > 0) {
                $querySettings->setStoragePageIds([$currentPageId]);
                $this->setDefaultQuerySettings($querySettings);
            }
        }
    }
    /**
     * Find route by slug and site identifier (using page context)
     */
    public function findBySlugAndSite(string $slug, string $siteIdentifier): ?FlightRoute
    {
        $siteRootPageIds = $this->getSiteRootPageIds($siteIdentifier);
        if (empty($siteRootPageIds)) {
            return null;
        }

        $query = $this->createQuery();
        $query->matching(
            $query->logicalAnd(
                $query->equals('routeSlug', $slug),
                $query->in('pid', $siteRootPageIds),
                $query->equals('isActive', true)
            )
        );
        return $query->execute()->getFirst();
    }

    /**
     * Find all active routes for a site (using page context)
     */
    public function findActiveBySite(string $siteIdentifier): array
    {
        $siteRootPageIds = $this->getSiteRootPageIds($siteIdentifier);
        if (empty($siteRootPageIds)) {
            return [];
        }

        $query = $this->createQuery();
        $query->matching(
            $query->logicalAnd(
                $query->in('pid', $siteRootPageIds),
                $query->equals('isActive', true)
            )
        );
        $query->setOrderings(['originCode' => 'ASC', 'destinationCode' => 'ASC']);
        return $query->execute()->toArray();
    }

    /**
     * Find routes by origin or destination (using page context)
     */
    public function findByOriginOrDestination(string $code, string $siteIdentifier): array
    {
        $siteRootPageIds = $this->getSiteRootPageIds($siteIdentifier);
        if (empty($siteRootPageIds)) {
            return [];
        }

        $query = $this->createQuery();
        $query->matching(
            $query->logicalAnd(
                $query->logicalOr(
                    $query->equals('originCode', $code),
                    $query->equals('destinationCode', $code)
                ),
                $query->in('pid', $siteRootPageIds),
                $query->equals('isActive', true)
            )
        );
        return $query->execute()->toArray();
    }

    /**
     * Find routes by landing page UID (using parent page ID)
     */
    public function findByLandingPage(int $landingPageUid): array
    {
        $query = $this->createQuery();
        $query->matching(
            $query->logicalAnd(
                $query->equals('pid', $landingPageUid),
                $query->equals('isActive', true)
            )
        );
        $query->setOrderings(['originCode' => 'ASC', 'destinationCode' => 'ASC']);
        return $query->execute()->toArray();
    }

    /**
     * Find all routes by landing page UID (including inactive ones) for backend preview
     */
    public function findAllByLandingPage(int $landingPageUid): array
    {
        $query = $this->createQuery();
        $query->matching(
            $query->equals('pid', $landingPageUid)
        );
        $query->setOrderings(['isActive' => 'DESC', 'originCode' => 'ASC', 'destinationCode' => 'ASC']);
        return $query->execute()->toArray();
    }

    /**
     * Find active routes by storage page ID (for plugin Record Storage Page functionality)
     */
    public function findActiveByStoragePage(int $storagePid): array
    {
        $query = $this->createQuery();

        // Override the default storage page settings for this specific query
        $querySettings = $query->getQuerySettings();
        $querySettings->setStoragePageIds([$storagePid]);
        $query->setQuerySettings($querySettings);

        $query->matching(
            $query->equals('isActive', true)
        );
        $query->setOrderings(['originCode' => 'ASC', 'destinationCode' => 'ASC']);
        return $query->execute()->toArray();
    }

    /**
     * Find all active routes using the default storage page settings
     * This respects the "Record Storage Page" field automatically
     */
    public function findAllActive(): array
    {
        $query = $this->createQuery();
        $query->matching(
            $query->equals('isActive', true)
        );
        $query->setOrderings(['originCode' => 'ASC', 'destinationCode' => 'ASC']);
        return $query->execute()->toArray();
    }

    /**
     * Find route by parameters and landing page (using parent page ID)
     */
    public function findByParametersAndLandingPage(array $routeParams, int $landingPageUid): ?FlightRoute
    {
        if (!isset($routeParams['origin']) || !isset($routeParams['destination'])) {
            return null;
        }

        $query = $this->createQuery();
        $query->matching(
            $query->logicalAnd(
                $query->equals('originCode', strtoupper($routeParams['origin'])),
                $query->equals('destinationCode', strtoupper($routeParams['destination'])),
                $query->equals('pid', $landingPageUid),
                $query->equals('isActive', true)
            )
        );
        return $query->execute()->getFirst();
    }

    /**
     * Find route by slug and landing page (using parent page ID)
     */
    public function findBySlugAndLandingPage(string $slug, int $landingPageUid): ?FlightRoute
    {
        $query = $this->createQuery();
        $query->matching(
            $query->logicalAnd(
                $query->equals('routeSlug', $slug),
                $query->equals('pid', $landingPageUid),
                $query->equals('isActive', true)
            )
        );
        return $query->execute()->getFirst();
    }

    /**
     * Get all page IDs that belong to a site (including subpages)
     */
    protected function getSiteRootPageIds(string $siteIdentifier): array
    {
        try {
            $siteFinder = GeneralUtility::makeInstance(SiteFinder::class);
            $site = $siteFinder->getSiteByIdentifier($siteIdentifier);
            $rootPageId = $site->getRootPageId();

            // Get all pages in the site tree
            $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
            $connection = $connectionPool->getConnectionForTable('pages');

            // For now, we'll include the root page and direct children
            // In a more complex setup, you might want to traverse the entire tree
            $pageIds = [$rootPageId];

            $childPages = $connection->select(
                ['uid'],
                'pages',
                [
                    'pid' => $rootPageId,
                    'deleted' => 0
                ]
            )->fetchAllAssociative();

            foreach ($childPages as $page) {
                $pageIds[] = (int)$page['uid'];
            }

            return $pageIds;
        } catch (\Exception $e) {
            return [];
        }
    }
}
