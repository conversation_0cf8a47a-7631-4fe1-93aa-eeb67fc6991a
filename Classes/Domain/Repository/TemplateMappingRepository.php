<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Domain\Repository;

use Bgs\FlightLandingPages\Domain\Model\TemplateMapping;
use TYPO3\CMS\Extbase\Persistence\Repository;

/**
 * Repository for template mappings
 */
class TemplateMappingRepository extends Repository
{
    /**
     * Find template mapping by landing page and route types
     */
    public function findByLandingPageAndTypes(int $landingPageUid, string $originType, string $destinationType): ?TemplateMapping
    {
        $query = $this->createQuery();
        $query->matching(
            $query->logicalAnd(
                $query->equals('landingPageUid', $landingPageUid),
                $query->equals('originType', $originType),
                $query->equals('destinationType', $destinationType)
            )
        );
        
        return $query->execute()->getFirst();
    }

    /**
     * Find all mappings for a landing page
     */
    public function findByLandingPage(int $landingPageUid): array
    {
        $query = $this->createQuery();
        $query->matching(
            $query->equals('landingPageUid', $landingPageUid)
        );
        $query->setOrderings([
            'originType' => 'ASC',
            'destinationType' => 'ASC'
        ]);
        
        return $query->execute()->toArray();
    }

    /**
     * Check if mapping exists for landing page and types
     */
    public function existsForLandingPageAndTypes(int $landingPageUid, string $originType, string $destinationType): bool
    {
        return $this->findByLandingPageAndTypes($landingPageUid, $originType, $destinationType) !== null;
    }
}
