<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\EventListener;

use Bgs\FlightLandingPages\Domain\Model\VirtualRouteContext;
use Bgs\FlightLandingPages\Service\VirtualRouteService;
use TYPO3\CMS\Frontend\Event\ModifyCacheLifetimeForPageEvent;
use TYPO3\CMS\Frontend\Event\BeforePageCacheIdentifierIsHashedEvent;

/**
 * Virtual Route Cache Listener
 *
 * Modifies TYPO3's page cache behavior for virtual routes to ensure
 * each virtual route gets its own cache entry instead of sharing
 * the cache with the template page.
 */
class VirtualRouteCacheListener
{
    /**
     * @var VirtualRouteService
     */
    private $virtualRouteService;

    public function __construct(VirtualRouteService $virtualRouteService)
    {
        $this->virtualRouteService = $virtualRouteService;
    }

    /**
     * Modify the cache identifier for virtual routes
     *
     * This ensures that each virtual route gets its own cache entry
     * instead of sharing the cache with the template page.
     */
    public function modifyCacheIdentifier(BeforePageCacheIdentifierIsHashedEvent $event): void
    {
        // Check if we're processing a virtual route
        if (!$this->virtualRouteService->isVirtualRoute()) {
            return;
        }

        $virtualRouteData = $this->virtualRouteService->getCurrentVirtualRoute();

        if (!$virtualRouteData || !isset($virtualRouteData['flightRoute'])) {
            return;
        }

        $flightRoute = $virtualRouteData['flightRoute'];
        $routeSlug = $flightRoute['route_slug'] ?? '';

        if (empty($routeSlug)) {
            return;
        }

        // Get the current cache identifier parts
        $cacheIdentifierParameters = $event->getCacheIdentifierParameters();

        // Add route-specific parameter to make cache unique per route
        $routeHash = md5($routeSlug);
        $cacheIdentifierParameters['virtual_route'] = $routeHash;

        // Debug logging (remove in production)
        error_log("VirtualRouteCacheListener: Adding cache parameter for route: {$routeSlug} -> {$routeHash}");

        // Update the cache identifier parameters
        $event->setCacheIdentifierParameters($cacheIdentifierParameters);
    }

    /**
     * Modify cache lifetime for virtual routes if needed
     * 
     * This allows virtual routes to have different cache lifetimes
     * than their template pages.
     */
    public function modifyCacheLifetime(ModifyCacheLifetimeForPageEvent $event): void
    {
        // Check if we're processing a virtual route
        if (!$this->virtualRouteService->isVirtualRoute()) {
            return;
        }

        $virtualRouteData = $this->virtualRouteService->getCurrentVirtualRoute();
        
        if (!$virtualRouteData || !isset($virtualRouteData['landingPage'])) {
            return;
        }

        $landingPage = $virtualRouteData['landingPage'];
        $cacheLifetime = (int)($landingPage['tx_flightlandingpages_cache_lifetime'] ?? 0);
        
        if ($cacheLifetime > 0) {
            $event->setCacheLifetime($cacheLifetime);
        }
    }
}
