<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\EventListener;

use Bgs\FlightLandingPages\Domain\Model\VirtualRouteContext;
use Bgs\FlightLandingPages\Service\VirtualRouteService;
use TYPO3\CMS\Frontend\Event\ModifyCacheLifetimeForPageEvent;
use TYPO3\CMS\Frontend\Event\ShouldUseCachedPageDataIfAvailableEvent;

/**
 * Virtual Route Cache Listener
 *
 * Disables TYPO3's page cache for virtual routes to prevent cache collisions
 * between different virtual routes using the same template page.
 */
class VirtualRouteCacheListener
{
    /**
     * @var VirtualRouteService
     */
    private $virtualRouteService;

    public function __construct(VirtualRouteService $virtualRouteService)
    {
        $this->virtualRouteService = $virtualRouteService;
    }

    /**
     * Disable page cache for virtual routes
     *
     * This prevents cache collisions between different virtual routes
     * that use the same template page.
     */
    public function disableCacheForVirtualRoutes(ShouldUseCachedPageDataIfAvailableEvent $event): void
    {
        // Check if we're processing a virtual route
        if (!$this->virtualRouteService->isVirtualRoute()) {
            return;
        }

        // Debug logging
        error_log("VirtualRouteCacheListener: Disabling cache for virtual route");

        // Disable cache for virtual routes
        $event->setShouldUseCachedPageData(false);
    }

    /**
     * Modify cache lifetime for virtual routes if needed
     *
     * This allows virtual routes to have different cache lifetimes
     * than their template pages.
     */
    public function modifyCacheLifetime(ModifyCacheLifetimeForPageEvent $event): void
    {
        // Check if we're processing a virtual route
        if (!$this->virtualRouteService->isVirtualRoute()) {
            return;
        }

        $virtualRouteData = $this->virtualRouteService->getCurrentVirtualRoute();

        if (!$virtualRouteData || !isset($virtualRouteData['landingPage'])) {
            return;
        }

        $landingPage = $virtualRouteData['landingPage'];
        $cacheLifetime = (int)($landingPage['tx_flightlandingpages_cache_lifetime'] ?? 0);

        if ($cacheLifetime > 0) {
            $event->setCacheLifetime($cacheLifetime);
        }
    }
}
