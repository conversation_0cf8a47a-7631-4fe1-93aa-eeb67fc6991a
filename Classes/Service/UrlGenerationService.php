<?php
namespace Bgs\FlightLandingPages\Service;

use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Site\SiteFinder;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use Bgs\FlightLandingPages\Domain\Model\FlightRoute;
use Bgs\FlightLandingPages\Domain\Model\LandingPage;

/**
 * Service for generating URLs for flight landing pages
 * 
 * Handles URL generation in the format: Site Endpoint + Landing Page Path + Pair Slug
 */
class UrlGenerationService
{
    protected SiteFinder $siteFinder;

    public function __construct(SiteFinder $siteFinder)
    {
        $this->siteFinder = $siteFinder;
    }

    /**
     * Generate URL for a flight route
     * 
     * @param FlightRoute $flightRoute The flight route
     * @param int $landingPageUid Landing page UID
     * @return string Generated URL
     */
    public function generateFlightRouteUrl(FlightRoute $flightRoute, int $landingPageUid): string
    {
        try {
            // Get site base URL
            $site = $this->siteFinder->getSiteByPageId($landingPageUid);
            $baseUrl = (string)$site->getBase();

            // Get landing page path
            $landingPagePath = $this->getLandingPagePath($landingPageUid);

            // Get route slug
            $routeSlug = $flightRoute->getRouteSlug();

            return $this->buildUrl($baseUrl, $landingPagePath, $routeSlug);
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Generate URL for a flight route by IDs
     * 
     * @param int $landingPageUid Landing page UID
     * @param string $routeSlug Route slug
     * @return string Generated URL
     */
    public function generateUrlByIds(int $landingPageUid, string $routeSlug): string
    {
        try {
            // Get site base URL
            $site = $this->siteFinder->getSiteByPageId($landingPageUid);
            $baseUrl = (string)$site->getBase();

            // Get landing page path
            $landingPagePath = $this->getLandingPagePath($landingPageUid);

            return $this->buildUrl($baseUrl, $landingPagePath, $routeSlug);
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Generate URL using LandingPage model
     * 
     * @param LandingPage $landingPage Landing page model
     * @param string $routeSlug Route slug
     * @param string $siteBaseUrl Site base URL
     * @return string Generated URL
     */
    public function generateUrlFromModel(LandingPage $landingPage, string $routeSlug, string $siteBaseUrl): string
    {
        return $landingPage->generateFullUrl($siteBaseUrl, $routeSlug);
    }

    /**
     * Build the final URL from components
     *
     * @param string $baseUrl Site base URL
     * @param string $landingPagePath Landing page path (not used - route slug contains full path)
     * @param string $routeSlug Route slug (contains complete path including landing page path)
     * @return string Final URL
     */
    protected function buildUrl(string $baseUrl, string $landingPagePath, string $routeSlug): string
    {
        // Ensure base URL ends with /
        $baseUrl = rtrim($baseUrl, '/') . '/';

        // Route slug already contains the full path including landing page path
        // (generated by SlugModifier), so we use it directly
        if (!empty($routeSlug)) {
            $routeSlug = ltrim($routeSlug, '/');
            return $baseUrl . $routeSlug;
        }

        // Fallback: if no route slug, return base URL
        return $baseUrl;
    }

    /**
     * Get the path to the landing page
     * 
     * @param int $landingPageUid Landing page UID
     * @return string Landing page path
     */
    protected function getLandingPagePath(int $landingPageUid): string
    {
        try {
            $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
                ->getQueryBuilderForTable('pages');

            $landingPage = $queryBuilder
                ->select('slug', 'title')
                ->from('pages')
                ->where(
                    $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($landingPageUid, \PDO::PARAM_INT))
                )
                ->executeQuery()
                ->fetchAssociative();

            if ($landingPage && !empty($landingPage['slug'])) {
                return ltrim($landingPage['slug'], '/');
            }

            return '';
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Validate URL components
     * 
     * @param string $baseUrl Site base URL
     * @param string $landingPagePath Landing page path
     * @param string $routeSlug Route slug
     * @return bool True if all components are valid
     */
    public function validateUrlComponents(string $baseUrl, string $landingPagePath, string $routeSlug): bool
    {
        return !empty($baseUrl) && !empty($routeSlug);
    }

    /**
     * Parse URL to extract components
     * 
     * @param string $url Full URL
     * @param string $baseUrl Site base URL
     * @return array Array with 'landingPagePath' and 'routeSlug'
     */
    public function parseUrl(string $url, string $baseUrl): array
    {
        // Remove base URL to get the path
        $path = str_replace($baseUrl, '', $url);
        $path = ltrim($path, '/');

        // Split path into components
        $pathParts = explode('/', $path);

        if (count($pathParts) >= 2) {
            // Last part is route slug, everything else is landing page path
            $routeSlug = array_pop($pathParts);
            $landingPagePath = implode('/', $pathParts);

            return [
                'landingPagePath' => $landingPagePath,
                'routeSlug' => $routeSlug
            ];
        }

        return [
            'landingPagePath' => '',
            'routeSlug' => $path
        ];
    }
}
