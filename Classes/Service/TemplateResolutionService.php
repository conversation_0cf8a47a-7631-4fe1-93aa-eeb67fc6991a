<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Service;

use Bgs\FlightLandingPages\Domain\Repository\TemplateMappingRepository;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Service for resolving template pages based on route type combinations
 */
class TemplateResolutionService
{
    protected TemplateMappingRepository $templateMappingRepository;

    public function __construct(TemplateMappingRepository $templateMappingRepository)
    {
        $this->templateMappingRepository = $templateMappingRepository;
    }

    /**
     * Resolve template page UID for a route type combination
     * 
     * Priority:
     * 1. Specific type combination mapping
     * 2. Default template page
     */
    public function resolveTemplatePageForRoute(int $landingPageUid, string $originType, string $destinationType): int
    {
        // 1. Try to find specific mapping
        $mapping = $this->templateMappingRepository->findByLandingPageAndTypes($landingPageUid, $originType, $destinationType);
        if ($mapping && $mapping->getTemplatePageUid() > 0) {
            return $mapping->getTemplatePageUid();
        }
        
        // 2. Fall back to default template
        return $this->getDefaultTemplate($landingPageUid);
    }

    /**
     * Get default template page UID for landing page
     */
    public function getDefaultTemplate(int $landingPageUid): int
    {
        try {
            $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
                ->getQueryBuilderForTable('pages');

            $result = $queryBuilder
                ->select('tx_flightlandingpages_template_page')
                ->from('pages')
                ->where(
                    $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($landingPageUid, \PDO::PARAM_INT)),
                    $queryBuilder->expr()->eq('deleted', 0)
                )
                ->executeQuery()
                ->fetchAssociative();

            return (int)($result['tx_flightlandingpages_template_page'] ?? 0);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get all available type combinations
     */
    public function getAvailableTypeCombinations(): array
    {
        return [
            'airport' => ['airport', 'city', 'country'],
            'city' => ['airport', 'city', 'country'],
            'country' => ['airport', 'city', 'country']
        ];
    }

    /**
     * Get all mappings for a landing page
     */
    public function getMappingsForLandingPage(int $landingPageUid): array
    {
        return $this->templateMappingRepository->findByLandingPage($landingPageUid);
    }
}
