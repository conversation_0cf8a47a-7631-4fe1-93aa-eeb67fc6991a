<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Service;

use TYPO3\CMS\Core\Database\Connection;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Service for updating slugs automatically
 * 
 * Handles the logic for updating flight route and landing page slugs
 * when parent page slugs change.
 */
class SlugUpdateService
{
    /**
     * Update all child slugs when a parent page slug changes
     * 
     * @param int $pageId Page ID that was updated
     * @param string $oldSlug Old slug value
     * @param string $newSlug New slug value
     * @return array Update statistics
     */
    public function updateChildSlugs(int $pageId, string $oldSlug, string $newSlug): array
    {
        $stats = [
            'landing_pages_updated' => 0,
            'flight_routes_updated' => 0,
            'errors' => []
        ];

        try {
            // Find all child landing pages (doktype 201)
            $landingPages = $this->findChildLandingPages($pageId);
            
            foreach ($landingPages as $landingPage) {
                $landingPageId = (int)$landingPage['uid'];
                
                // Update landing page slug if it contains the old parent path
                if ($this->updateLandingPageSlug($landingPageId, $oldSlug, $newSlug)) {
                    $stats['landing_pages_updated']++;
                }
                
                // Update all flight routes under this landing page
                $routesUpdated = $this->updateFlightRouteSlugs($landingPageId);
                $stats['flight_routes_updated'] += $routesUpdated;
            }

            // Also check for direct flight routes under the changed page
            $directRoutesUpdated = $this->updateFlightRouteSlugs($pageId);
            $stats['flight_routes_updated'] += $directRoutesUpdated;

        } catch (\Exception $e) {
            $stats['errors'][] = $e->getMessage();
        }

        return $stats;
    }

    /**
     * Update flight route slugs for a specific landing page
     *
     * @param int $landingPageId Landing page ID
     * @return int Number of routes updated
     */
    public function updateFlightRouteSlugs(int $landingPageId): int
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_flightlandingpages_domain_model_flightroute');

        $flightRoutes = $queryBuilder
            ->select('uid', 'origin_code', 'destination_code', 'origin_type', 'destination_type', 'origin_name', 'destination_name', 'route_slug')
            ->from('tx_flightlandingpages_domain_model_flightroute')
            ->where(
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($landingPageId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAllAssociative();

        $updated = 0;
        $connection = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getConnectionForTable('tx_flightlandingpages_domain_model_flightroute');

        foreach ($flightRoutes as $route) {
            $newSlug = $this->generateFlightRouteSlug($route, $landingPageId);
            
            // Only update if the slug actually changed
            if (!empty($newSlug) && $newSlug !== $route['route_slug']) {
                $connection->update(
                    'tx_flightlandingpages_domain_model_flightroute',
                    ['route_slug' => $newSlug],
                    ['uid' => (int)$route['uid']]
                );
                $updated++;
            }
        }

        return $updated;
    }

    /**
     * Find all child landing pages recursively
     * 
     * @param int $pageId Parent page ID
     * @return array Landing page records
     */
    protected function findChildLandingPages(int $pageId): array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        // Find all pages in the page tree under this page
        $allChildPages = $this->findAllChildPages($pageId);
        
        if (empty($allChildPages)) {
            return [];
        }

        $childPageIds = array_column($allChildPages, 'uid');
        $childPageIds[] = $pageId; // Include the page itself

        return $queryBuilder
            ->select('uid', 'pid', 'slug', 'title')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->in('uid', $queryBuilder->createNamedParameter($childPageIds, Connection::PARAM_INT_ARRAY)),
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(201, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAllAssociative();
    }

    /**
     * Find all child pages recursively
     * 
     * @param int $pageId Parent page ID
     * @return array Child page records
     */
    protected function findAllChildPages(int $pageId): array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $allPages = [];
        $pagesToProcess = [$pageId];

        while (!empty($pagesToProcess)) {
            $currentPageId = array_shift($pagesToProcess);
            
            $childPages = $queryBuilder
                ->select('uid', 'pid', 'slug', 'title', 'doktype')
                ->from('pages')
                ->where(
                    $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($currentPageId, \PDO::PARAM_INT)),
                    $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
                )
                ->executeQuery()
                ->fetchAllAssociative();

            foreach ($childPages as $childPage) {
                $allPages[] = $childPage;
                $pagesToProcess[] = (int)$childPage['uid'];
            }
        }

        return $allPages;
    }

    /**
     * Update landing page slug if needed
     * 
     * @param int $landingPageId Landing page ID
     * @param string $oldParentSlug Old parent slug
     * @param string $newParentSlug New parent slug
     * @return bool True if updated, false otherwise
     */
    protected function updateLandingPageSlug(int $landingPageId, string $oldParentSlug, string $newParentSlug): bool
    {
        $currentSlug = $this->getPageSlug($landingPageId);
        
        if (empty($currentSlug)) {
            return false;
        }

        // Check if the current slug starts with the old parent slug
        $oldParentSlug = rtrim($oldParentSlug, '/');
        $newParentSlug = rtrim($newParentSlug, '/');
        
        if (strpos($currentSlug, $oldParentSlug) === 0) {
            $newSlug = $newParentSlug . substr($currentSlug, strlen($oldParentSlug));
            
            $connection = GeneralUtility::makeInstance(ConnectionPool::class)
                ->getConnectionForTable('pages');
                
            $connection->update(
                'pages',
                ['slug' => $newSlug],
                ['uid' => $landingPageId]
            );
            
            return true;
        }

        return false;
    }

    /**
     * Generate flight route slug using the same logic as SlugModifier
     * 
     * @param array $route Flight route record
     * @param int $landingPageId Landing page ID
     * @return string Generated slug
     */
    protected function generateFlightRouteSlug(array $route, int $landingPageId): string
    {
        // Get parent page path
        $parentPagePath = $this->getPageSlug($landingPageId);
        
        // Generate type-aware slug
        $originPart = $this->generateSlugPart(
            $route['origin_name'] ?? '',
            $route['origin_code'] ?? '',
            $route['origin_type'] ?? 'airport'
        );

        $destinationPart = $this->generateSlugPart(
            $route['destination_name'] ?? '',
            $route['destination_code'] ?? '',
            $route['destination_type'] ?? 'airport'
        );

        if (empty($originPart) || empty($destinationPart)) {
            return '';
        }

        $baseSlug = $originPart . '-' . $destinationPart;
        
        // Combine with parent page path
        if (!empty($parentPagePath)) {
            $parentPagePath = ltrim($parentPagePath, '/');
            return $parentPagePath . '/' . $baseSlug;
        }

        return $baseSlug;
    }

    /**
     * Generate slug part based on type (same logic as SlugModifier)
     * 
     * @param string $name Location name
     * @param string $code Location code
     * @param string $type Location type
     * @return string Slug part
     */
    protected function generateSlugPart(string $name, string $code, string $type): string
    {
        if ($type === 'airport') {
            return strtolower($code);
        }

        // For city and country, use sanitized name
        return $this->sanitizeNameForSlug($name);
    }

    /**
     * Sanitize name for slug (same logic as SlugModifier)
     * 
     * @param string $name Location name
     * @return string Sanitized slug part
     */
    protected function sanitizeNameForSlug(string $name): string
    {
        if (empty($name)) {
            return '';
        }

        // Convert to ASCII (remove accents)
        $name = iconv('UTF-8', 'ASCII//TRANSLIT', $name);

        // Convert to lowercase
        $name = strtolower(trim($name));

        // Replace spaces with underscores
        $name = str_replace(' ', '_', $name);

        // Remove special characters except underscores and hyphens
        $name = preg_replace('/[^a-z0-9_\-]/', '', $name);

        // Handle multiple consecutive separators
        $name = preg_replace('/[_\-]+/', '_', $name);

        return trim($name, '_-');
    }

    /**
     * Get current slug for a page
     * 
     * @param int $pageId Page ID
     * @return string Current slug
     */
    protected function getPageSlug(int $pageId): string
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $result = $queryBuilder
            ->select('slug')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($pageId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative();

        return $result['slug'] ?? '';
    }
}
