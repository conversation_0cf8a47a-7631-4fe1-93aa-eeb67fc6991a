<?php
namespace Bgs\FlightLandingPages\Controller;

use Bgs\FlightLandingPages\Service\UrlGenerationService;
use Psr\Http\Message\ResponseInterface;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Site\SiteFinder;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;
use Bgs\FlightLandingPages\Domain\Repository\FlightRouteRepository;

class DestinationsMenuController extends ActionController
{
    protected FlightRouteRepository $flightRouteRepository;
    protected UrlGenerationService $urlGenerationService;
    protected SiteFinder $siteFinder;

    public function injectFlightRouteRepository(FlightRouteRepository $flightRouteRepository): void
    {
        $this->flightRouteRepository = $flightRouteRepository;
    }

    public function injectUrlGenerationService(UrlGenerationService $urlGenerationService): void
    {
        $this->urlGenerationService = $urlGenerationService;
    }

    public function injectSiteFinder(SiteFinder $siteFinder): void
    {
        $this->siteFinder = $siteFinder;
    }

    /**
     * List all available flight destinations for the current site
     */
    public function listAction(): ResponseInterface
    {
        // Get display mode from FlexForm
        $displayMode = $this->settings['displayMode'] ?? 'list';
        $showOriginFilter = (bool)($this->settings['showOriginFilter'] ?? false);
        $showDestinationFilter = (bool)($this->settings['showDestinationFilter'] ?? false);

        // Get storage page for debugging/display purposes
        $storagePid = $this->getStoragePid();

        // Get all active routes - this automatically respects the Record Storage Page setting
        // If no Record Storage Page is set, it falls back to the current page
        $routes = $this->flightRouteRepository->findAllActive();

        // Generate full URLs for each route
        $routesWithUrls = [];
        $currentPageId = (int)$GLOBALS['TSFE']->id;

        try {
            $site = $this->siteFinder->getSiteByPageId($currentPageId);
            $siteBaseUrl = (string)$site->getBase();
        } catch (\Exception $e) {
            $siteBaseUrl = '';
        }

        foreach ($routes as $route) {
            $routeData = [
                'originCode' => $route->getOriginCode(),
                'originName' => $route->getOriginName(),
                'originType' => $route->getOriginType(),
                'destinationCode' => $route->getDestinationCode(),
                'destinationName' => $route->getDestinationName(),
                'destinationType' => $route->getDestinationType(),
                'routeSlug' => $route->getRouteSlug(),
                'fullUrl' => $this->generateFullUrl($siteBaseUrl, $route->getRouteSlug())
            ];
            $routesWithUrls[] = $routeData;
        }

        // Get unique origins and destinations for filters
        $origins = [];
        $destinations = [];
        foreach ($routes as $route) {
            $origins[$route->getOriginCode()] = $route->getOriginName();
            $destinations[$route->getDestinationCode()] = $route->getDestinationName();
        }

        $this->view->assignMultiple([
            'routes' => $routesWithUrls,
            'displayMode' => $displayMode,
            'showOriginFilter' => $showOriginFilter,
            'showDestinationFilter' => $showDestinationFilter,
            'origins' => $origins,
            'destinations' => $destinations,
            'storagePid' => $storagePid
        ]);

        return $this->htmlResponse();
    }

    /**
     * Get the effective storage page ID for debugging/display purposes
     * This shows which page is actually being used for record storage
     */
    protected function getStoragePid(): int
    {
        // Get the repository's default query settings which include the storagePid
        // This automatically respects the "Record Storage Page" field from the plugin
        $querySettings = $this->flightRouteRepository->createQuery()->getQuerySettings();
        $storagePids = $querySettings->getStoragePageIds();

        // If storage page is configured and not empty, use the first one
        if (!empty($storagePids) && !in_array(0, $storagePids, true)) {
            return (int)$storagePids[0];
        }

        // Fallback to current page ID (where the plugin is placed)
        return (int)$GLOBALS['TSFE']->id;
    }

    /**
     * Generate full URL from site base URL and route slug
     *
     * @param string $siteBaseUrl Site base URL
     * @param string $routeSlug Route slug
     * @return string Full URL
     */
    protected function generateFullUrl(string $siteBaseUrl, string $routeSlug): string
    {
        if (empty($routeSlug)) {
            return $siteBaseUrl;
        }

        // Ensure base URL ends with /
        $baseUrl = rtrim($siteBaseUrl, '/') . '/';

        // Remove leading slash from route slug if present
        $routeSlug = ltrim($routeSlug, '/');

        return $baseUrl . $routeSlug;
    }
}
